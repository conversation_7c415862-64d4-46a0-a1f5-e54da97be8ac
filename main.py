"""
交易系统主程序

基于事件驱动架构的量化交易系统启动器。
整合所有模块，提供统一的配置和运行接口。
"""

import logging
import pandas as pd
from datetime import datetime
from typing import Dict, Any

# 导入新的事件驱动架构模块
from trading_system.core import EventEngine, MarketEvent, SignalEvent, OrderEvent, FillEvent, TurnPointEvent
from trading_system.data import HistoricalDataProvider
from trading_system.analysis import IndicatorCalculator, TurnPointDetector
from trading_system.strategies import TurnPointStrategy, EnhancedTurnPointStrategy, FixedEnhancedTurnPointStrategy
from trading_system.execution import BacktestExecutionHandler
from trading_system.portfolio import PortfolioManager
from trading_system.utils import TradingVisualizer


class TradingSystem:
    """
    交易系统主类

    整合所有组件，提供统一的配置和运行接口。
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化交易系统

        Args:
            config: 系统配置
        """
        self.config = config
        self.logger = self._setup_logging()

        # 核心组件
        self.event_engine = EventEngine()
        self.data_provider = None
        self.indicator_calculator = IndicatorCalculator()
        self.turn_point_detector = TurnPointDetector(
            min_keep_days=config.get('min_keep_days', 1),  # 降低最小持续天数
            min_extent_percent=config.get('min_extent_percent', 1.0),  # 降低最小变化幅度
            volume_window=15,
            volume_threshold=1.3,
            sr_sensitivity=0.02,
            realtime_mode=True  # 启用实时模式
        )
        self.strategy = None
        self.execution_handler = BacktestExecutionHandler(
            event_engine=self.event_engine,
            slippage_pct=config.get('slippage_pct', 0.001)
        )
        self.portfolio_manager = PortfolioManager(
            initial_cash=config.get('initial_cash', 100000)
        )
        self.visualizer = TradingVisualizer()

        # 运行状态
        self.is_running = False
        self.market_data_history = []
        self.turn_points = []

    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('trading_system.log')
            ]
        )
        return logging.getLogger(__name__)

    def initialize(self):
        """初始化系统组件"""
        self.logger.info("Initializing trading system...")

        # 1. 初始化数据提供者
        self.data_provider = HistoricalDataProvider(
            symbol=self.config['symbol'],
            start_date=self.config['start_date'],
            end_date=self.config['end_date']
        )

        # 2. 初始化修复版增强转折点策略
        self.strategy = FixedEnhancedTurnPointStrategy(self.config['symbol'])

        # 设置优化后的策略参数
        self.strategy.set_parameter('position_size', self.config.get('position_size', 100))
        self.strategy.set_parameter('min_volume_confirm_strength', self.config.get('min_volume_confirm_strength', 0.4))
        self.strategy.set_parameter('min_breakout_strength', self.config.get('min_breakout_strength', 0.6))
        self.strategy.set_parameter('stop_loss_percent', self.config.get('stop_loss_percent', 0.04))
        self.strategy.set_parameter('take_profit_percent', self.config.get('take_profit_percent', 0.15))
        self.strategy.set_parameter('max_position_hold_days', self.config.get('max_position_hold_days', 10))

        # 3. 创建默认指标
        self.indicator_calculator.create_default_indicators()

        # 4. 注册事件处理器
        self._register_event_handlers()

        self.logger.info("System initialized successfully")

    def _register_event_handlers(self):
        """注册事件处理器"""
        # 市场数据事件处理器
        self.event_engine.register_handler(MarketEvent, self._on_market_event)

        # 转折点事件处理器
        self.event_engine.register_handler(TurnPointEvent, self._on_turn_point_event)

        # 交易信号事件处理器
        self.event_engine.register_handler(SignalEvent, self._on_signal_event)

        # 订单事件处理器
        self.event_engine.register_handler(OrderEvent, self._on_order_event)

        # 成交事件处理器
        self.event_engine.register_handler(FillEvent, self._on_fill_event)

    def _on_market_event(self, event: MarketEvent):
        """处理市场数据事件"""
        # 1. 计算技术指标
        indicators = self.indicator_calculator.calculate_all(event)
        event.indicators.update(indicators)

        # 2. 检测转折点
        turn_point_event = self.turn_point_detector.process_market_event(event)
        if turn_point_event:
            self.turn_points.append(turn_point_event)
            self.event_engine.put_event(turn_point_event)

        # 3. 策略处理
        signals = self.strategy.process_event(event)
        for signal in signals:
            self.event_engine.put_event(signal)

        # 4. 更新投资组合市值
        self.portfolio_manager.update_market_value(event)

        # 5. 更新执行处理器的市场数据
        self.execution_handler.update_market_data(event)

        # 6. 保存历史数据
        self.market_data_history.append(event)

    def _on_turn_point_event(self, event: TurnPointEvent):
        """处理转折点事件"""
        self.logger.info(f"Processing turn point event: {event.point_type} at {event.price:.2f} on {event.timestamp}")

        # 策略处理转折点
        signals = self.strategy.process_event(event)
        self.logger.info(f"Turn point event generated {len(signals)} signals")

        for signal in signals:
            self.event_engine.put_event(signal)

    def _on_signal_event(self, event: SignalEvent):
        """处理交易信号事件"""
        # 生成订单
        orders = self.execution_handler.process_signal(event)
        for order in orders:
            self.event_engine.put_event(order)

    def _on_order_event(self, event: OrderEvent):
        """处理订单事件"""
        # 执行订单
        fill_event = self.execution_handler.execute_order(event)
        if fill_event:
            self.event_engine.put_event(fill_event)

    def _on_fill_event(self, event: FillEvent):
        """处理成交事件"""
        # 更新策略状态
        self.strategy.on_fill_event(event)

        # 更新投资组合
        self.portfolio_manager.process_fill_event(event)

    def run_backtest(self):
        """运行回测"""
        self.logger.info("Starting backtest...")
        self.is_running = True

        # 启动事件引擎
        self.event_engine.start()

        try:
            # 获取数据流并处理
            data_count = 0
            for market_event in self.data_provider.get_data_stream():
                if not self.is_running:
                    break

                # 将市场数据事件放入队列
                self.event_engine.put_event(market_event)
                data_count += 1

                if data_count % 100 == 0:
                    self.logger.info(f"已处理 {data_count} 条 market events")

            # 等待所有事件处理完成
            self.event_engine.wait_for_completion(timeout=30)

            self.logger.info(f"回测结束. 处理完成 {data_count} 条 market events")

        except Exception as e:
            self.logger.error(f"Error during backtest: {e}")
            raise
        finally:
            # 停止事件引擎
            self.event_engine.stop()
            self.is_running = False

    def get_results(self) -> Dict[str, Any]:
        """获取回测结果"""
        # 获取绩效统计
        performance_stats = self.portfolio_manager.get_performance_stats()

        # 获取执行统计
        execution_stats = self.execution_handler.get_stats()

        # 获取策略统计
        strategy_stats = self.strategy.get_stats()

        # 准备市场数据DataFrame
        market_df = pd.DataFrame([
            {
                'date': event.timestamp,
                'open': event.open,
                'high': event.high,
                'low': event.low,
                'close': event.close,
                'volume': event.volume
            }
            for event in self.market_data_history
        ])

        return {
            'performance': performance_stats,
            'execution': execution_stats,
            'strategy': strategy_stats,
            'market_data': market_df,
            'turn_points': self.turn_points,
            'portfolio_manager': self.portfolio_manager
        }

    def show_results(self, save_charts: bool = False):
        """显示回测结果"""
        results = self.get_results()

        # 显示增强策略的详细信息
        self._print_enhanced_strategy_info()

        # 打印增强的绩效报告（包含详细交易记录）
        print(f"\n{'=' * 100}")
        print(f"{'详细交易记录与绩效分析':^100}")
        print(f"{'=' * 100}")
        self.visualizer.print_performance_report(self.portfolio_manager)

        # 绘制图表（可选）
        if save_charts and len(self.market_data_history) > 0:
            chart_path = f"trading_results_{self.config['symbol']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"

            self.visualizer.plot_trading_results(
                portfolio_manager=self.portfolio_manager,
                market_data=results['market_data'],
                turn_points=self.turn_points,
                save_path=chart_path
            )

            # 绘制绩效指标
            metrics_path = f"performance_metrics_{self.config['symbol']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            self.visualizer.plot_performance_metrics(
                portfolio_manager=self.portfolio_manager,
                save_path=metrics_path
            )

            print(f"\n📊 图表已保存:")
            print(f"  交易结果图: {chart_path}")
            print(f"  绩效指标图: {metrics_path}")

        print(f"\n{'=' * 100}")
        print("回测完成！详细交易记录已显示完毕。")
        print("=" * 100)

    def _print_enhanced_strategy_info(self):
        """显示增强策略信息"""
        print("\n" + "=" * 60)
        print("增强转折点策略分析报告")
        print("=" * 60)

        # 获取策略统计
        strategy_stats = self.strategy.get_stats()

        print(f"策略名称: {strategy_stats['name']}")
        print(f"交易标的: {strategy_stats['symbol']}")
        print(f"当前持仓: {strategy_stats['current_position']}")
        print(f"生成信号数: {strategy_stats['signals_count']}")

        # 显示信号统计
        if 'signal_stats' in strategy_stats:
            signal_stats = strategy_stats['signal_stats']
            print(f"\n信号统计:")
            print(f"  总信号数: {signal_stats['total_signals']}")
            print(f"  买入信号: {signal_stats['buy_signals']}")
            print(f"  卖出信号: {signal_stats['sell_signals']}")
            print(f"  量价确认: {signal_stats['volume_confirmed']}")
            print(f"  支撑阻力确认: {signal_stats['sr_confirmed']}")
            print(f"  过滤信号: {signal_stats['filtered_out']}")

        # 显示检测器统计
        if 'detector_stats' in strategy_stats:
            detector_stats = strategy_stats['detector_stats']
            print(f"\n转折点检测统计:")
            print(f"  确认转折点: {detector_stats['confirmed_points']}")
            print(f"  支撑阻力位: {detector_stats['sr_levels']}")
            print(f"  量价信号: {detector_stats['volume_signals']}")

        # 显示当前市场分析
        if hasattr(self.strategy, 'get_market_analysis'):
            analysis = self.strategy.get_market_analysis()
            if analysis.get('volume_analysis'):
                va = analysis['volume_analysis']
                print(f"\n当前市场分析:")
                print(f"  当前量比: {va.get('volume_ratio', 0):.2f}")
                print(f"  成交量趋势: {va.get('volume_trend', 0):.4f}")

            if analysis.get('support_resistance_levels'):
                sr_levels = analysis['support_resistance_levels'][:3]
                print(f"  主要支撑阻力位:")
                for i, level in enumerate(sr_levels):
                    print(f"    {i+1}. {level['level_type']} {level['price']:.2f} (强度: {level['strength']:.2f})")

        print("=" * 60)

    def stop(self):
        """停止系统"""
        self.is_running = False
        self.event_engine.stop()
        self.logger.info("Trading system stopped")


def create_default_config() -> Dict[str, Any]:
    """创建默认配置"""
    return {
        # 基本配置
        'symbol': '003021',
        'start_date': '20200101',
        'end_date': '20250630',
        'initial_cash': 100000,
        'slippage_pct': 0.001,

        # 转折点检测器配置
        'min_keep_days': 2,
        'min_extent_percent': 2.0,

        # 修复版增强策略配置
        'position_size': 100,
        'min_volume_confirm_strength': 0.6,
        'min_breakout_strength': 0.8,
        'stop_loss_percent': 0.05,
        'take_profit_percent': 0.10,
        'max_position_hold_days': 15,
        'min_trade_interval_days': 1,  # 最小交易间隔
        'max_position_size': 1000      # 最大持仓限制
    }


def main():
    """主函数"""
    print("=" * 100)
    print("交易系统 v2.2 - 修复版增强转折点策略")
    print("基于量价分析的智能交易系统 - 解决交易异常问题")
    print("=" * 100)

    # 创建配置
    config = create_default_config()

    # 显示配置信息
    print(f"交易标的: {config['symbol']}")
    print(f"回测期间: {config['start_date']} - {config['end_date']}")
    print(f"初始资金: {config['initial_cash']:,} 元")
    print(f"策略类型: 修复版增强转折点策略 (解决交易异常)")
    print("-" * 100)

    # 创建交易系统
    trading_system = TradingSystem(config)

    try:
        # 初始化系统
        trading_system.initialize()

        # 运行回测
        trading_system.run_backtest()

        # 显示结果
        trading_system.show_results(save_charts=True)

    except KeyboardInterrupt:
        print("\n用户中断执行")
        trading_system.stop()
    except Exception as e:
        print(f"\n系统运行出错: {e}")
        trading_system.logger.error(f"System error: {e}", exc_info=True)
    finally:
        trading_system.stop()


if __name__ == "__main__":
    main()